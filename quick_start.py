#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 快速启动脚本
自动化环境配置和模型下载
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

class BagelQuickStart:
    def __init__(self, model_dir="./models", conda_env="bagel"):
        self.model_dir = Path(model_dir)
        self.conda_env = conda_env
        self.model_path = self.model_dir / "BAGEL-7B-MoT"
        
    def run_command(self, cmd, check=True):
        """运行命令"""
        print(f"🔄 执行: {cmd}")
        try:
            result = subprocess.run(cmd, shell=True, check=check, 
                                  capture_output=True, text=True)
            if result.stdout:
                print(result.stdout)
            return result.returncode == 0
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            if e.stderr:
                print(e.stderr)
            return False
    
    def check_conda(self):
        """检查 conda 是否安装"""
        return self.run_command("conda --version", check=False)
    
    def create_conda_env(self):
        """创建 conda 环境"""
        print(f"🐍 创建 conda 环境: {self.conda_env}")
        
        if not self.check_conda():
            print("❌ 未检测到 conda，请先安装 Anaconda 或 Miniconda")
            return False
        
        # 检查环境是否已存在
        result = subprocess.run(f"conda env list | grep {self.conda_env}", 
                              shell=True, capture_output=True)
        if result.returncode == 0:
            print(f"✅ 环境 {self.conda_env} 已存在")
            return True
        
        # 创建新环境
        return self.run_command(f"conda create -n {self.conda_env} python=3.10 -y")
    
    def install_dependencies(self):
        """安装依赖包"""
        print("📦 安装依赖包...")
        
        # 激活环境并安装依赖
        activate_cmd = f"conda activate {self.conda_env}"
        install_cmd = f"{activate_cmd} && pip install -r requirements.txt"
        
        return self.run_command(install_cmd)
    
    def download_model(self):
        """下载模型"""
        print("📥 下载 BAGEL 模型...")
        
        # 创建模型目录
        self.model_dir.mkdir(exist_ok=True)
        
        if self.model_path.exists():
            print(f"✅ 模型已存在: {self.model_path}")
            return True
        
        # 创建下载脚本
        download_script = f"""
import os
from huggingface_hub import snapshot_download

save_dir = "{self.model_path}"
repo_id = "ByteDance-Seed/BAGEL-7B-MoT"
cache_dir = os.path.join(save_dir, "cache")

print(f"开始下载模型到: {{save_dir}}")

try:
    snapshot_download(
        cache_dir=cache_dir,
        local_dir=save_dir,
        repo_id=repo_id,
        local_dir_use_symlinks=False,
        resume_download=True,
        allow_patterns=["*.json", "*.safetensors", "*.bin", "*.py", "*.md", "*.txt"],
    )
    print("模型下载完成！")
except Exception as e:
    print(f"下载失败: {{e}}")
    exit(1)
"""
        
        # 保存并执行下载脚本
        script_path = "download_model_temp.py"
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(download_script)
        
        activate_cmd = f"conda activate {self.conda_env}"
        download_cmd = f"{activate_cmd} && python {script_path}"
        
        success = self.run_command(download_cmd)
        
        # 清理临时文件
        if os.path.exists(script_path):
            os.remove(script_path)
        
        return success
    
    def update_inference_notebook(self):
        """更新推理笔记本中的模型路径"""
        print("📝 更新推理笔记本配置...")
        
        notebook_path = "inference.ipynb"
        if not os.path.exists(notebook_path):
            print(f"❌ 未找到 {notebook_path}")
            return False
        
        # 读取笔记本内容
        with open(notebook_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换模型路径
        old_path = '"/path/to/BAGEL-7B-MoT/weights"'
        new_path = f'"{self.model_path}/weights"'
        
        if old_path in content:
            content = content.replace(old_path, new_path)
            
            # 写回文件
            with open(notebook_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ 已更新模型路径为: {new_path}")
            return True
        else:
            print("⚠️  未找到需要替换的模型路径")
            return True
    
    def run_config_check(self):
        """运行配置检查"""
        print("🔍 运行配置检查...")
        
        activate_cmd = f"conda activate {self.conda_env}"
        check_cmd = f"{activate_cmd} && python config_checker.py {self.model_path}/weights"
        
        return self.run_command(check_cmd)
    
    def print_usage_instructions(self):
        """打印使用说明"""
        print("\n" + "="*60)
        print("🎉 BAGEL 快速启动完成！")
        print("="*60)
        print(f"📁 模型路径: {self.model_path}")
        print(f"🐍 Conda 环境: {self.conda_env}")
        print("\n📖 使用说明:")
        print(f"1. 激活环境: conda activate {self.conda_env}")
        print("2. 启动 Jupyter: jupyter notebook")
        print("3. 打开 inference.ipynb 开始使用")
        print("\n🔧 其他命令:")
        print("- 训练模型: bash scripts/train.sh")
        print("- 评估模型: bash scripts/eval/run_eval_vlm.sh")
        print("- 配置检查: python config_checker.py")
        print("="*60)
    
    def run(self):
        """运行完整的快速启动流程"""
        print("🚀 开始 BAGEL 快速启动...")
        
        steps = [
            ("创建 Conda 环境", self.create_conda_env),
            ("安装依赖包", self.install_dependencies),
            ("下载模型", self.download_model),
            ("更新配置", self.update_inference_notebook),
            ("配置检查", self.run_config_check),
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 步骤: {step_name}")
            if not step_func():
                print(f"❌ 步骤失败: {step_name}")
                return False
            print(f"✅ 步骤完成: {step_name}")
        
        self.print_usage_instructions()
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BAGEL 快速启动脚本")
    parser.add_argument("--model-dir", default="./models", 
                       help="模型保存目录 (默认: ./models)")
    parser.add_argument("--conda-env", default="bagel",
                       help="Conda 环境名称 (默认: bagel)")
    parser.add_argument("--skip-download", action="store_true",
                       help="跳过模型下载")
    
    args = parser.parse_args()
    
    starter = BagelQuickStart(args.model_dir, args.conda_env)
    
    if args.skip_download:
        # 跳过下载，只做环境配置
        steps = [
            starter.create_conda_env,
            starter.install_dependencies,
            starter.update_inference_notebook,
            starter.run_config_check,
        ]
        
        for step in steps:
            if not step():
                sys.exit(1)
        
        starter.print_usage_instructions()
    else:
        # 完整流程
        success = starter.run()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
