#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 Flash Attention 依赖问题
为 CPU 环境创建兼容性解决方案
"""

import os
import sys
import shutil

def create_flash_attn_mock():
    """创建 Flash Attention 的模拟模块"""
    print("🔧 创建 Flash Attention 模拟模块...")
    
    # 创建模拟的 flash_attn 模块
    flash_attn_content = '''"""
Flash Attention 模拟模块 (CPU 兼容)
用于在没有 CUDA 的环境中运行 BAGEL
"""

import torch
import torch.nn.functional as F

def flash_attn_varlen_func(
    q, k, v, cu_seqlens_q, cu_seqlens_k, max_seqlen_q, max_seqlen_k,
    dropout_p=0.0, softmax_scale=None, causal=False, window_size=(-1, -1),
    alibi_slopes=None, deterministic=False, return_attn_probs=False
):
    """
    Flash Attention 的 CPU 兼容实现
    使用标准的 PyTorch 注意力机制
    """
    print("⚠️  使用 CPU 兼容的注意力实现 (性能较低)")
    
    # 简化的注意力计算
    batch_size = len(cu_seqlens_q) - 1
    
    # 重新组织输入张量
    outputs = []
    
    for i in range(batch_size):
        start_q = cu_seqlens_q[i]
        end_q = cu_seqlens_q[i + 1]
        start_k = cu_seqlens_k[i]
        end_k = cu_seqlens_k[i + 1]
        
        q_i = q[start_q:end_q]
        k_i = k[start_k:end_k]
        v_i = v[start_k:end_k]
        
        # 标准注意力计算
        if softmax_scale is None:
            softmax_scale = 1.0 / (q_i.size(-1) ** 0.5)
        
        scores = torch.matmul(q_i, k_i.transpose(-2, -1)) * softmax_scale
        
        if causal:
            seq_len = scores.size(-1)
            causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
            scores.masked_fill_(causal_mask, float('-inf'))
        
        attn_weights = F.softmax(scores, dim=-1)
        
        if dropout_p > 0.0:
            attn_weights = F.dropout(attn_weights, p=dropout_p, training=True)
        
        output = torch.matmul(attn_weights, v_i)
        outputs.append(output)
    
    # 合并输出
    result = torch.cat(outputs, dim=0)
    
    if return_attn_probs:
        return result, None  # 不返回注意力权重
    else:
        return result

def flash_attn_func(q, k, v, dropout_p=0.0, softmax_scale=None, causal=False, 
                   window_size=(-1, -1), alibi_slopes=None, deterministic=False, 
                   return_attn_probs=False):
    """
    Flash Attention 函数的 CPU 兼容实现
    """
    print("⚠️  使用 CPU 兼容的注意力实现")
    
    if softmax_scale is None:
        softmax_scale = 1.0 / (q.size(-1) ** 0.5)
    
    scores = torch.matmul(q, k.transpose(-2, -1)) * softmax_scale
    
    if causal:
        seq_len = scores.size(-2)
        causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
        scores.masked_fill_(causal_mask, float('-inf'))
    
    attn_weights = F.softmax(scores, dim=-1)
    
    if dropout_p > 0.0:
        attn_weights = F.dropout(attn_weights, p=dropout_p, training=True)
    
    output = torch.matmul(attn_weights, v)
    
    if return_attn_probs:
        return output, attn_weights
    else:
        return output

# 兼容性别名
flash_attn_with_kvcache = flash_attn_func
flash_attn_kvpacked_func = flash_attn_func
'''
    
    # 创建 flash_attn 目录
    flash_attn_dir = "flash_attn_mock"
    os.makedirs(flash_attn_dir, exist_ok=True)
    
    # 写入 __init__.py
    with open(os.path.join(flash_attn_dir, "__init__.py"), "w", encoding="utf-8") as f:
        f.write(flash_attn_content)
    
    print("✅ Flash Attention 模拟模块创建完成")
    return True

def patch_qwen2_navit():
    """修补 qwen2_navit.py 文件以使用模拟的 flash_attn"""
    print("🔧 修补 qwen2_navit.py 文件...")
    
    navit_file = "modeling/bagel/qwen2_navit.py"
    
    if not os.path.exists(navit_file):
        print(f"❌ 文件不存在: {navit_file}")
        return False
    
    # 备份原文件
    backup_file = navit_file + ".backup"
    if not os.path.exists(backup_file):
        shutil.copy2(navit_file, backup_file)
        print(f"✅ 已备份原文件: {backup_file}")
    
    # 读取文件内容
    with open(navit_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 替换 flash_attn 导入
    old_import = "from flash_attn import flash_attn_varlen_func"
    new_import = """try:
    from flash_attn import flash_attn_varlen_func
    FLASH_ATTN_AVAILABLE = True
except ImportError:
    # 使用 CPU 兼容的实现
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'flash_attn_mock'))
    from flash_attn_mock import flash_attn_varlen_func
    FLASH_ATTN_AVAILABLE = False
    print("⚠️  Flash Attention 不可用，使用 CPU 兼容实现")"""
    
    if old_import in content:
        content = content.replace(old_import, new_import)
        
        # 写回文件
        with open(navit_file, "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ qwen2_navit.py 修补完成")
        return True
    else:
        print("⚠️  未找到需要替换的导入语句")
        return True

def install_flash_attn_alternative():
    """尝试安装 Flash Attention 或使用替代方案"""
    print("⚡ 尝试安装 Flash Attention...")
    
    import subprocess
    
    # 尝试安装 flash-attn
    try:
        result = subprocess.run(
            ["pip", "install", "flash-attn", "--no-build-isolation"],
            capture_output=True, text=True, timeout=300
        )
        
        if result.returncode == 0:
            print("✅ Flash Attention 安装成功")
            return True
        else:
            print("⚠️  Flash Attention 安装失败，使用模拟实现")
            return False
            
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Flash Attention 安装失败，使用模拟实现")
        return False

def test_fixed_imports():
    """测试修复后的导入"""
    print("🧪 测试修复后的模块导入...")
    
    try:
        # 测试 flash_attn 导入
        sys.path.insert(0, "flash_attn_mock")
        from flash_attn_mock import flash_attn_varlen_func
        print("✅ Flash Attention 模拟模块导入成功")
        
        # 测试 BAGEL 模块导入
        from modeling.bagel import BagelConfig
        print("✅ BAGEL 模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复 Flash Attention 依赖问题")
    print("="*50)
    
    steps = [
        ("尝试安装 Flash Attention", install_flash_attn_alternative),
        ("创建 Flash Attention 模拟模块", create_flash_attn_mock),
        ("修补 qwen2_navit.py", patch_qwen2_navit),
        ("测试修复后的导入", test_fixed_imports),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\\n📋 步骤: {step_name}")
        if step_func():
            print(f"✅ {step_name} - 完成")
            success_count += 1
        else:
            print(f"⚠️  {step_name} - 部分失败，继续...")
    
    print(f"\\n📊 修复完成: {success_count}/{len(steps)} 步骤成功")
    
    if success_count >= 3:  # 至少3个步骤成功
        print("🎉 Flash Attention 依赖问题修复完成！")
        print("\\n📋 下一步:")
        print("1. 运行: python test_bagel_core.py")
        print("2. 如果测试通过，可以继续使用 BAGEL")
        return True
    else:
        print("❌ 修复未完全成功，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
