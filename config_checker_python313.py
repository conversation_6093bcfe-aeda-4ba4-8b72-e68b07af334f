#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 项目配置检查器
用于验证环境配置是否正确
"""

import os
import sys
import subprocess
import importlib
import torch
from pathlib import Path

class BagelConfigChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success = []
        
    def log_error(self, msg):
        self.errors.append(f"❌ {msg}")
        
    def log_warning(self, msg):
        self.warnings.append(f"⚠️  {msg}")
        
    def log_success(self, msg):
        self.success.append(f"✅ {msg}")
        
    def check_python_version(self):
        """检查 Python 版本"""
        version = sys.version_info
        if version.major == 3 and version.minor in [10, 11, 12, 13]:
            self.log_success(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
        else:
            self.log_warning(f"推荐 Python 3.10，当前版本: {version.major}.{version.minor}.{version.micro}")
    
    def check_cuda_availability(self):
        """检查 CUDA 可用性"""
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            self.log_success(f"CUDA 可用，检测到 {device_count} 个 GPU")
            
            for i in range(device_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                self.log_success(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            self.log_error("CUDA 不可用，请检查 GPU 驱动和 CUDA 安装")
    
    def check_required_packages(self):
        """检查必需的 Python 包"""
        required_packages = {
            'torch': '2.5.1',
            'torchvision': '0.20.1', 
            'transformers': '4.49.0',
            'accelerate': '0.34.0',
            'huggingface_hub': '0.29.1',
            'safetensors': '0.4.5',
            'einops': '0.8.1',
            'numpy': '1.24.4',
            'PIL': None,  # Pillow
            'cv2': None,  # opencv-python
            'matplotlib': '3.7.0',
            'yaml': None,  # PyYAML
            'scipy': '1.10.1',
        }
        
        for package, expected_version in required_packages.items():
            try:
                if package == 'PIL':
                    import PIL
                    module = PIL
                    package_name = 'Pillow'
                elif package == 'cv2':
                    import cv2
                    module = cv2
                    package_name = 'opencv-python'
                elif package == 'yaml':
                    import yaml
                    module = yaml
                    package_name = 'PyYAML'
                else:
                    module = importlib.import_module(package)
                    package_name = package
                
                if hasattr(module, '__version__'):
                    version = module.__version__
                    if expected_version and version != expected_version:
                        self.log_warning(f"{package_name}: 期望版本 {expected_version}，当前版本 {version}")
                    else:
                        self.log_success(f"{package_name}: {version}")
                else:
                    self.log_success(f"{package_name}: 已安装")
                    
            except ImportError:
                self.log_error(f"缺少包: {package_name}")
    
    def check_flash_attention(self):
        """检查 Flash Attention"""
        try:
            import flash_attn
            self.log_success(f"Flash Attention: {flash_attn.__version__}")
        except ImportError:
            self.log_warning("Flash Attention 未安装，可能影响训练速度")
    
    def check_project_structure(self):
        """检查项目结构"""
        required_dirs = [
            'data',
            'modeling', 
            'train',
            'eval',
            'scripts'
        ]
        
        required_files = [
            'requirements.txt',
            'inference.ipynb',
            'inferencer.py',
            'README.md'
        ]
        
        for dir_name in required_dirs:
            if os.path.exists(dir_name):
                self.log_success(f"目录存在: {dir_name}/")
            else:
                self.log_error(f"缺少目录: {dir_name}/")
        
        for file_name in required_files:
            if os.path.exists(file_name):
                self.log_success(f"文件存在: {file_name}")
            else:
                self.log_error(f"缺少文件: {file_name}")
    
    def check_model_path(self, model_path=None):
        """检查模型路径配置"""
        if model_path is None:
            self.log_warning("未指定模型路径，请下载模型并设置路径")
            return
            
        if not os.path.exists(model_path):
            self.log_error(f"模型路径不存在: {model_path}")
            return
            
        required_model_files = [
            'llm_config.json',
            'vit_config.json',
            'ae.safetensors',
            'ema.safetensors'
        ]
        
        for file_name in required_model_files:
            file_path = os.path.join(model_path, file_name)
            if os.path.exists(file_path):
                self.log_success(f"模型文件存在: {file_name}")
            else:
                self.log_error(f"缺少模型文件: {file_name}")
    
    def check_disk_space(self):
        """检查磁盘空间"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_gb = free / (1024**3)
            
            if free_gb > 50:
                self.log_success(f"可用磁盘空间: {free_gb:.1f} GB")
            elif free_gb > 20:
                self.log_warning(f"磁盘空间较少: {free_gb:.1f} GB")
            else:
                self.log_error(f"磁盘空间不足: {free_gb:.1f} GB")
        except Exception as e:
            self.log_warning(f"无法检查磁盘空间: {e}")
    
    def run_all_checks(self, model_path=None):
        """运行所有检查"""
        print("🔍 开始检查 BAGEL 项目配置...\n")
        
        self.check_python_version()
        self.check_cuda_availability()
        self.check_required_packages()
        self.check_flash_attention()
        self.check_project_structure()
        self.check_model_path(model_path)
        self.check_disk_space()
        
        # 输出结果
        print("\n" + "="*50)
        print("📋 配置检查结果")
        print("="*50)
        
        if self.success:
            print("\n✅ 成功项目:")
            for msg in self.success:
                print(f"  {msg}")
        
        if self.warnings:
            print("\n⚠️  警告项目:")
            for msg in self.warnings:
                print(f"  {msg}")
        
        if self.errors:
            print("\n❌ 错误项目:")
            for msg in self.errors:
                print(f"  {msg}")
        
        print(f"\n📊 总结: {len(self.success)} 成功, {len(self.warnings)} 警告, {len(self.errors)} 错误")
        
        if self.errors:
            print("\n🔧 请解决上述错误后再运行 BAGEL")
            return False
        else:
            print("\n🎉 配置检查通过！可以开始使用 BAGEL")
            return True

def main():
    """主函数"""
    checker = BagelConfigChecker()
    
    # 可以指定模型路径
    model_path = None
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    
    success = checker.run_all_checks(model_path)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
