#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL Python 3.13 兼容性修复脚本
针对 Python 3.13 环境的特殊处理
"""

import os
import sys
import subprocess

class Python313CompatibilityFixer:
    def __init__(self):
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
    def run_command(self, cmd, check=True):
        """运行命令"""
        print(f"🔄 执行: {cmd}")
        try:
            result = subprocess.run(cmd, shell=True, check=check, 
                                  capture_output=True, text=True)
            if result.stdout:
                print(result.stdout)
            return result.returncode == 0
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            if e.stderr:
                print(e.stderr)
            return False
    
    def install_compatible_pytorch(self):
        """安装兼容 Python 3.13 的 PyTorch"""
        print("📦 安装兼容 Python 3.13 的 PyTorch...")
        
        # 使用最新版本的 PyTorch (支持 Python 3.13)
        pytorch_cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
        return self.run_command(pytorch_cmd)
    
    def install_compatible_packages(self):
        """安装兼容 Python 3.13 的包"""
        print("📦 安装兼容包...")
        
        # 使用兼容版本或最新版本
        compatible_packages = [
            "transformers>=4.40.0",  # 使用较新版本
            "accelerate",            # 最新版本
            "huggingface_hub",       # 最新版本
            "safetensors",           # 最新版本
            "numpy",                 # 最新版本
            "scipy",                 # 最新版本
            "matplotlib",            # 最新版本
            "einops",                # 已安装成功
            "sentencepiece",         # 尝试最新版本
            "opencv-python",         # 最新版本
            "pyarrow",               # 最新版本
            "decord",                # 可能需要特殊处理
            "wandb",                 # 最新版本
            "jupyter",               # Jupyter 支持
            "ipywidgets",            # Jupyter widgets
        ]
        
        success_count = 0
        for package in compatible_packages:
            print(f"📦 安装: {package}")
            if self.run_command(f"pip install {package}", check=False):
                success_count += 1
            else:
                print(f"⚠️  {package} 安装失败，继续...")
        
        print(f"📊 包安装结果: {success_count}/{len(compatible_packages)} 成功")
        return success_count > len(compatible_packages) * 0.7  # 70% 成功率
    
    def install_decord_alternative(self):
        """安装 decord 的替代方案"""
        print("📹 处理视频解码库...")
        
        # 尝试安装 decord
        if self.run_command("pip install decord", check=False):
            print("✅ decord 安装成功")
            return True
        
        # 如果失败，尝试替代方案
        print("⚠️  decord 安装失败，尝试替代方案...")
        alternatives = [
            "imageio[ffmpeg]",  # 视频处理替代
            "av",               # PyAV 视频库
        ]
        
        for alt in alternatives:
            if self.run_command(f"pip install {alt}", check=False):
                print(f"✅ 安装替代库: {alt}")
                return True
        
        print("⚠️  视频解码库安装失败，可能影响视频相关功能")
        return False
    
    def create_compatibility_requirements(self):
        """创建 Python 3.13 兼容的 requirements 文件"""
        print("📝 创建 Python 3.13 兼容的依赖文件...")
        
        requirements_content = """# BAGEL Python 3.13 兼容依赖文件
# 核心深度学习框架 (使用最新版本)
torch>=2.6.0
torchvision>=0.21.0
torchaudio

# Transformers 生态 (使用兼容版本)
transformers>=4.40.0
accelerate
huggingface_hub
safetensors
sentencepiece

# 数值计算和科学计算 (使用最新版本)
numpy
scipy
einops

# 图像和视频处理
opencv-python
Pillow
# decord  # 可能不兼容，使用替代方案
imageio[ffmpeg]
av

# 数据处理
pyarrow
PyYAML

# 可视化和工具
matplotlib
wandb

# Jupyter 支持
jupyter
ipywidgets

# 其他工具
requests
tqdm
"""
        
        with open("requirements_python313.txt", "w", encoding="utf-8") as f:
            f.write(requirements_content)
        
        print("✅ 已创建 requirements_python313.txt")
        return True
    
    def update_config_checker(self):
        """更新配置检查器以适应 Python 3.13"""
        print("🔧 更新配置检查器...")
        
        # 读取原始配置检查器
        try:
            with open("config_checker.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # 修改 Python 版本检查
            old_check = 'if version.major == 3 and version.minor == 10:'
            new_check = 'if version.major == 3 and version.minor in [10, 11, 12, 13]:'
            
            if old_check in content:
                content = content.replace(old_check, new_check)
                
                # 写回文件
                with open("config_checker_python313.py", "w", encoding="utf-8") as f:
                    f.write(content)
                
                print("✅ 已创建 config_checker_python313.py")
                return True
            else:
                print("⚠️  未找到需要修改的 Python 版本检查")
                return True
                
        except FileNotFoundError:
            print("⚠️  config_checker.py 不存在")
            return False
    
    def create_demo_script(self):
        """创建简化的演示脚本"""
        print("📝 创建演示脚本...")
        
        demo_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 简化演示脚本 (Python 3.13 兼容)
"""

import sys
import os

def test_basic_imports():
    """测试基础导入"""
    print("🧪 测试基础包导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
        
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        
        import PIL
        print(f"✅ Pillow: {PIL.__version__}")
        
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
        
        import yaml
        print("✅ PyYAML: 已导入")
        
        import einops
        print(f"✅ Einops: {einops.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_bagel_modules():
    """检查 BAGEL 模块"""
    print("🔍 检查 BAGEL 模块...")
    
    required_dirs = ['data', 'modeling', 'train', 'eval']
    missing_dirs = []
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ 目录存在: {dir_name}/")
        else:
            print(f"❌ 缺少目录: {dir_name}/")
            missing_dirs.append(dir_name)
    
    return len(missing_dirs) == 0

def main():
    """主函数"""
    print("🥯 BAGEL Python 3.13 兼容性测试")
    print("="*50)
    
    # 测试基础导入
    imports_ok = test_basic_imports()
    
    # 检查模块
    modules_ok = check_bagel_modules()
    
    print("\\n" + "="*50)
    if imports_ok and modules_ok:
        print("🎉 基础环境检查通过！")
        print("\\n📋 下一步:")
        print("1. 下载模型权重")
        print("2. 运行 inference.ipynb")
        return True
    else:
        print("❌ 环境检查失败，请检查依赖安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        
        with open("demo_python313.py", "w", encoding="utf-8") as f:
            f.write(demo_content)
        
        print("✅ 已创建 demo_python313.py")
        return True
    
    def run_fix(self):
        """运行完整修复流程"""
        print("🔧 开始 Python 3.13 兼容性修复...")
        print(f"🐍 当前 Python 版本: {self.python_version}")
        
        steps = [
            ("创建兼容依赖文件", self.create_compatibility_requirements),
            ("安装兼容 PyTorch", self.install_compatible_pytorch),
            ("安装兼容包", self.install_compatible_packages),
            ("处理视频解码库", self.install_decord_alternative),
            ("更新配置检查器", self.update_config_checker),
            ("创建演示脚本", self.create_demo_script),
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\\n📋 步骤: {step_name}")
            if step_func():
                print(f"✅ 步骤完成: {step_name}")
                success_count += 1
            else:
                print(f"⚠️  步骤部分失败: {step_name}")
        
        print(f"\\n📊 修复完成: {success_count}/{len(steps)} 步骤成功")
        
        if success_count >= len(steps) - 1:
            print("🎉 Python 3.13 兼容性修复基本完成！")
            print("\\n📋 下一步:")
            print("1. 运行: python demo_python313.py")
            print("2. 如果测试通过，可以尝试使用 BAGEL")
            print("3. 注意：某些功能可能需要 Python 3.10 环境")
            return True
        else:
            print("❌ 兼容性修复未完全成功")
            print("💡 建议创建 Python 3.10 环境以获得最佳兼容性")
            return False

def main():
    """主函数"""
    fixer = Python313CompatibilityFixer()
    success = fixer.run_fix()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
