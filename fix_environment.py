#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 环境修复脚本
自动修复环境配置问题
"""

import os
import sys
import subprocess
import platform

class BagelEnvironmentFixer:
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
    def run_command(self, cmd, check=True):
        """运行命令"""
        print(f"🔄 执行: {cmd}")
        try:
            result = subprocess.run(cmd, shell=True, check=check, 
                                  capture_output=True, text=True)
            if result.stdout:
                print(result.stdout)
            return result.returncode == 0
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            if e.stderr:
                print(e.stderr)
            return False
    
    def check_cuda_available(self):
        """检查 CUDA 是否可用"""
        try:
            result = subprocess.run("nvidia-smi", shell=True, 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 检测到 NVIDIA GPU")
                print(result.stdout)
                return True
            else:
                print("❌ 未检测到 NVIDIA GPU 或驱动")
                return False
        except:
            print("❌ nvidia-smi 命令不可用")
            return False
    
    def install_pytorch_cuda(self):
        """安装 CUDA 版本的 PyTorch"""
        print("📦 安装 CUDA 版本的 PyTorch...")
        
        if not self.check_cuda_available():
            print("⚠️  未检测到 CUDA，将安装 CPU 版本")
            pytorch_cmd = "pip install torch==2.5.1 torchvision==0.20.1 torchaudio --index-url https://download.pytorch.org/whl/cpu"
        else:
            # 安装 CUDA 版本
            pytorch_cmd = "pip install torch==2.5.1 torchvision==0.20.1 torchaudio --index-url https://download.pytorch.org/whl/cu121"
        
        return self.run_command(pytorch_cmd)
    
    def install_flash_attention(self):
        """安装 Flash Attention"""
        print("⚡ 安装 Flash Attention...")
        
        if not self.check_cuda_available():
            print("⚠️  Flash Attention 需要 CUDA，跳过安装")
            return True
        
        # 先安装编译依赖
        if self.system == "linux":
            self.run_command("sudo apt-get update && sudo apt-get install -y ninja-build", check=False)
        
        # 安装 Flash Attention
        flash_cmd = "pip install flash-attn==2.5.8 --no-build-isolation"
        return self.run_command(flash_cmd, check=False)  # 允许失败
    
    def fix_package_versions(self):
        """修复包版本"""
        print("🔧 修复包版本...")
        
        # 核心包版本修复
        packages = [
            "transformers==4.49.0",
            "accelerate>=0.34.0", 
            "huggingface_hub==0.29.1",
            "safetensors==0.4.5",
            "numpy==1.24.4",
            "scipy==1.10.1",
            "matplotlib==3.7.0",
            "einops==0.8.1",
            "sentencepiece==0.1.99",
            "PyYAML==6.0.2",
            "opencv-python==********",
            "pyarrow==11.0.0",
            "decord==0.6.0",
            "wandb",
        ]
        
        for package in packages:
            print(f"📦 安装/更新: {package}")
            if not self.run_command(f"pip install {package}", check=False):
                print(f"⚠️  {package} 安装失败，继续...")
        
        return True
    
    def create_requirements_fixed(self):
        """创建修复后的 requirements.txt"""
        print("📝 创建修复后的依赖文件...")
        
        requirements_content = """# BAGEL 修复后的依赖文件
# 核心深度学习框架
torch==2.5.1
torchvision==0.20.1
torchaudio

# Transformers 生态
transformers==4.49.0
accelerate>=0.34.0
huggingface_hub==0.29.1
safetensors==0.4.5
sentencepiece==0.1.99

# 数值计算和科学计算
numpy==1.24.4
scipy==1.10.1
einops==0.8.1

# 图像和视频处理
opencv-python==********
decord==0.6.0
Pillow

# 数据处理
pyarrow==11.0.0
PyYAML==6.0.2

# 可视化和工具
matplotlib==3.7.0
wandb

# Flash Attention (可选，需要 CUDA)
# flash-attn==2.5.8

# 其他工具
requests
"""
        
        with open("requirements_fixed.txt", "w", encoding="utf-8") as f:
            f.write(requirements_content)
        
        print("✅ 已创建 requirements_fixed.txt")
        return True
    
    def install_jupyter(self):
        """安装 Jupyter"""
        print("📓 安装 Jupyter...")
        return self.run_command("pip install jupyter ipywidgets")
    
    def check_python_version(self):
        """检查 Python 版本"""
        print(f"🐍 当前 Python 版本: {self.python_version}")
        
        if self.python_version != "3.10":
            print("⚠️  推荐使用 Python 3.10")
            print("建议创建新的 conda 环境:")
            print("conda create -n bagel python=3.10 -y")
            print("conda activate bagel")
            return False
        return True
    
    def test_imports(self):
        """测试关键包导入"""
        print("🧪 测试关键包导入...")
        
        test_packages = [
            "torch",
            "torchvision", 
            "transformers",
            "accelerate",
            "huggingface_hub",
            "safetensors",
            "numpy",
            "PIL",
            "cv2",
            "yaml",
            "einops",
        ]
        
        failed_imports = []
        
        for package in test_packages:
            try:
                if package == "PIL":
                    import PIL
                elif package == "cv2":
                    import cv2
                elif package == "yaml":
                    import yaml
                else:
                    __import__(package)
                print(f"✅ {package} 导入成功")
            except ImportError as e:
                print(f"❌ {package} 导入失败: {e}")
                failed_imports.append(package)
        
        if failed_imports:
            print(f"\n❌ 以下包导入失败: {failed_imports}")
            return False
        else:
            print("\n✅ 所有关键包导入成功！")
            return True
    
    def run_fix(self):
        """运行完整修复流程"""
        print("🔧 开始修复 BAGEL 环境...")
        
        # 检查 Python 版本
        if not self.check_python_version():
            print("⚠️  建议先创建正确的 Python 环境")
        
        # 修复步骤
        steps = [
            ("创建修复后的依赖文件", self.create_requirements_fixed),
            ("安装 PyTorch (CUDA版本)", self.install_pytorch_cuda),
            ("修复包版本", self.fix_package_versions),
            ("安装 Flash Attention", self.install_flash_attention),
            ("安装 Jupyter", self.install_jupyter),
            ("测试包导入", self.test_imports),
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\n📋 步骤: {step_name}")
            if step_func():
                print(f"✅ 步骤完成: {step_name}")
                success_count += 1
            else:
                print(f"⚠️  步骤部分失败: {step_name}")
        
        print(f"\n📊 修复完成: {success_count}/{len(steps)} 步骤成功")
        
        if success_count >= len(steps) - 1:  # 允许一个步骤失败
            print("🎉 环境修复基本完成！")
            print("\n📋 下一步:")
            print("1. 运行: python config_checker.py")
            print("2. 下载模型: python quick_start.py")
            print("3. 开始使用: jupyter notebook inference.ipynb")
            return True
        else:
            print("❌ 环境修复未完全成功，请检查错误信息")
            return False

def main():
    """主函数"""
    fixer = BagelEnvironmentFixer()
    success = fixer.run_fix()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
