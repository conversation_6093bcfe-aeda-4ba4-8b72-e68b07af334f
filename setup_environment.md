# BAGEL 环境配置指南

## 1. 创建 Conda 环境
```bash
# 创建新的 conda 环境
conda create -n bagel python=3.10 -y
conda activate bagel
```

## 2. 安装依赖包
```bash
# 安装基础依赖
pip install -r requirements.txt
```

## 3. 依赖包说明

### 核心依赖
- **torch==2.5.1** - PyTorch 深度学习框架
- **torchvision==0.20.1** - 计算机视觉库
- **transformers==4.49.0** - Hugging Face 模型库
- **flash_attn==2.5.8** - Flash Attention 加速库
- **accelerate>=0.34.0** - 模型加速库

### 数据处理
- **decord==0.6.0** - 视频解码库
- **opencv_python==********** - 图像处理库
- **pyarrow==11.0.0** - 数据序列化库
- **numpy==1.24.4** - 数值计算库

### 模型相关
- **huggingface_hub==0.29.1** - 模型下载库
- **safetensors==0.4.5** - 安全张量存储
- **sentencepiece==0.1.99** - 文本分词库

### 工具库
- **einops==0.8.1** - 张量操作库
- **matplotlib==3.7.0** - 绘图库
- **PyYAML==6.0.2** - YAML 配置文件解析
- **scipy==1.10.1** - 科学计算库
- **wandb** - 实验跟踪工具

## 4. GPU 要求
- **推荐**: NVIDIA GPU with 40GB+ VRAM
- **最低**: 支持 CUDA 的 GPU
- **多GPU**: 支持多GPU推理和训练

## 5. 系统要求
- **操作系统**: Linux/Windows
- **Python**: 3.10
- **CUDA**: 兼容 PyTorch 2.5.1
