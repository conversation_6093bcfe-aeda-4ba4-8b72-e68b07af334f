#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 模型下载脚本
"""

import os
from huggingface_hub import snapshot_download

def download_bagel_model(save_dir="./models/BAGEL-7B-MoT"):
    """下载 BAGEL 模型"""
    print(f"开始下载 BAGEL 模型到: {save_dir}")
    
    repo_id = "ByteDance-Seed/BAGEL-7B-MoT"
    cache_dir = os.path.join(save_dir, "cache")
    
    try:
        snapshot_download(
            cache_dir=cache_dir,
            local_dir=save_dir,
            repo_id=repo_id,
            local_dir_use_symlinks=False,
            resume_download=True,
            allow_patterns=[
                "*.json", 
                "*.safetensors", 
                "*.bin", 
                "*.py", 
                "*.md", 
                "*.txt"
            ],
        )
        print("✅ 模型下载完成！")
        print(f"模型路径: {save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        model_dir = sys.argv[1]
    else:
        model_dir = "./models/BAGEL-7B-MoT"
    
    success = download_bagel_model(model_dir)
    sys.exit(0 if success else 1)
