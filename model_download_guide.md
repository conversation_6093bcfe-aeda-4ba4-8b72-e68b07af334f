# BAGEL 模型下载指南

## 1. 自动下载脚本
创建模型下载脚本：

```python
# download_model.py
from huggingface_hub import snapshot_download
import os

def download_bagel_model(save_dir="/path/to/save/BAGEL-7B-MoT"):
    """
    下载 BAGEL-7B-MoT 模型
    
    Args:
        save_dir: 模型保存路径
    """
    repo_id = "ByteDance-Seed/BAGEL-7B-MoT"
    cache_dir = os.path.join(save_dir, "cache")
    
    print(f"开始下载模型到: {save_dir}")
    
    snapshot_download(
        cache_dir=cache_dir,
        local_dir=save_dir,
        repo_id=repo_id,
        local_dir_use_symlinks=False,
        resume_download=True,
        allow_patterns=[
            "*.json", 
            "*.safetensors", 
            "*.bin", 
            "*.py", 
            "*.md", 
            "*.txt"
        ],
    )
    
    print("模型下载完成！")

if __name__ == "__main__":
    # 修改为你的模型保存路径
    model_save_path = "E:/models/BAGEL-7B-MoT"
    download_bagel_model(model_save_path)
```

## 2. 模型文件结构
下载完成后，模型目录应包含：

```
BAGEL-7B-MoT/
├── weights/
│   ├── llm_config.json          # LLM 配置文件
│   ├── vit_config.json          # ViT 配置文件
│   ├── ae.safetensors           # VAE 模型权重
│   ├── ema.safetensors          # 主模型权重
│   └── tokenizer files          # 分词器文件
├── README.md
└── config.json
```

## 3. 配置路径
在 inference.ipynb 中修改模型路径：

```python
# 修改这行为你的实际模型路径
model_path = "E:/models/BAGEL-7B-MoT/weights"
```

## 4. 验证下载
运行以下代码验证模型下载是否完整：

```python
import os

def verify_model_files(model_path):
    """验证模型文件是否完整"""
    required_files = [
        "llm_config.json",
        "vit_config.json", 
        "ae.safetensors",
        "ema.safetensors"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(os.path.join(model_path, file)):
            missing_files.append(file)
    
    if missing_files:
        print(f"缺少文件: {missing_files}")
        return False
    else:
        print("所有必需文件都存在！")
        return True

# 验证模型
model_path = "E:/models/BAGEL-7B-MoT/weights"
verify_model_files(model_path)
```

## 5. 网络问题解决
如果下载遇到网络问题：

1. **使用镜像源**:
```bash
export HF_ENDPOINT=https://hf-mirror.com
```

2. **分段下载**:
```python
# 可以分别下载不同类型的文件
allow_patterns=["*.json"]  # 先下载配置文件
allow_patterns=["*.safetensors"]  # 再下载模型权重
```

3. **断点续传**:
```python
resume_download=True  # 启用断点续传
```
