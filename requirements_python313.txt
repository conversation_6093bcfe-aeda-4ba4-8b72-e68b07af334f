# BAGEL Python 3.13 兼容依赖文件
# 核心深度学习框架 (使用最新版本)
torch>=2.6.0
torchvision>=0.21.0
torchaudio

# Transformers 生态 (使用兼容版本)
transformers>=4.40.0
accelerate
huggingface_hub
safetensors
sentencepiece

# 数值计算和科学计算 (使用最新版本)
numpy
scipy
einops

# 图像和视频处理
opencv-python
Pillow
# decord  # 可能不兼容，使用替代方案
imageio[ffmpeg]
av

# 数据处理
pyarrow
PyYAML

# 可视化和工具
matplotlib
wandb

# Jupyter 支持
jupyter
ipywidgets

# 其他工具
requests
tqdm
