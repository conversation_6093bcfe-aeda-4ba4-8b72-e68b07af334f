# BAGEL 推理配置指南

## 1. 基础推理配置

### GPU 内存配置
根据你的 GPU 内存调整：

```python
# 在 inference.ipynb 中修改
max_mem_per_gpu = "40GiB"  # 根据你的 GPU 内存调整

# 常见配置：
# RTX 4090: "24GiB"
# A100: "40GiB" 或 "80GiB"
# V100: "32GiB"
```

### 设备映射配置
```python
# 单GPU配置
if torch.cuda.device_count() == 1:
    device_map = "auto"

# 多GPU配置
device_map = infer_auto_device_map(
    model,
    max_memory={i: max_mem_per_gpu for i in range(torch.cuda.device_count())},
    no_split_module_classes=["Bagel", "Qwen2MoTDecoderLayer"],
)
```

## 2. 推理参数配置

### 图像生成参数
```python
inference_hyper = dict(
    cfg_text_scale=4.0,        # 文本引导强度 (1.0-8.0)
    cfg_img_scale=1.0,         # 图像引导强度 (1.0-3.0)
    cfg_interval=[0.4, 1.0],   # CFG 应用区间
    timestep_shift=3.0,        # 时间步偏移 (1.0-5.0)
    num_timesteps=50,          # 去噪步数 (20-100)
    cfg_renorm_min=1.0,        # CFG 重归一化最小值
    cfg_renorm_type="global",  # CFG 重归一化类型
)
```

### 图像编辑参数
```python
inference_hyper = dict(
    cfg_text_scale=4.0,           # 文本引导强度
    cfg_img_scale=2.0,            # 图像引导强度 (编辑时通常更高)
    cfg_interval=[0.0, 1.0],      # 全程应用 CFG
    timestep_shift=4.0,           # 更高的时间步偏移
    num_timesteps=50,             # 去噪步数
    cfg_renorm_min=1.0,           # CFG 重归一化
    cfg_renorm_type="text_channel", # 文本通道重归一化
)
```

### 思维链推理参数
```python
inference_hyper = dict(
    max_think_token_n=1000,    # 最大思考token数
    do_sample=False,           # 是否采样
    text_temperature=0.3,      # 文本生成温度
    # ... 其他参数同上
)
```

## 3. 图像变换配置

### VAE 图像变换
```python
vae_transform = ImageTransform(
    max_size=1024,    # 最大图像尺寸
    min_size=512,     # 最小图像尺寸  
    stride=16         # 步长
)
```

### ViT 图像变换
```python
vit_transform = ImageTransform(
    max_size=980,     # 最大图像尺寸
    min_size=224,     # 最小图像尺寸
    stride=14         # 步长
)
```

## 4. 使用示例

### 文本到图像生成
```python
prompt = "一个穿着仙女裙的女性角色扮演者"
output_dict = inferencer(text=prompt, **inference_hyper)
display(output_dict['image'])
```

### 图像编辑
```python
image = Image.open('test_images/women.jpg')
prompt = '她穿着同样的衣服，在现代地铁里安静地读报纸'
output_dict = inferencer(image=image, text=prompt, **inference_hyper)
display(output_dict['image'])
```

### 图像理解
```python
image = Image.open('test_images/meme.jpg')
prompt = "请解释这个表情包的幽默之处"
output_dict = inferencer(
    image=image, 
    text=prompt, 
    understanding_output=True,
    **inference_hyper
)
print(output_dict['text'])
```

### 思维链生成
```python
prompt = "一辆由小汽车组成的大汽车"
output_dict = inferencer(
    text=prompt, 
    think=True, 
    **inference_hyper
)
print("思考过程:", output_dict['text'])
display(output_dict['image'])
```

## 5. 性能优化建议

1. **内存优化**:
   - 使用 `torch.cuda.empty_cache()` 清理GPU内存
   - 调整 `max_mem_per_gpu` 参数

2. **速度优化**:
   - 减少 `num_timesteps` 加快生成
   - 使用更少的 `max_think_token_n`

3. **质量优化**:
   - 增加 `num_timesteps` 提高质量
   - 调整 `cfg_text_scale` 控制文本遵循度
