# BAGEL 训练配置指南

## 1. 训练脚本配置

### 修改训练脚本
编辑 `scripts/train.sh`：

```bash
#!/bin/bash
# 设置训练参数
export num_nodes=1              # 节点数量
export node_rank=0              # 当前节点排名
export master_addr="localhost"  # 主节点地址
export master_port=29500        # 主节点端口

# 模型路径配置
export llm_path="/path/to/qwen2-7b"           # LLM 基础模型路径
export vae_path="/path/to/vae/model"          # VAE 模型路径  
export vit_path="/path/to/siglip/model"       # ViT 模型路径

# 训练输出配置
export output_path="/path/to/output"          # 训练输出路径
export ckpt_path="/path/to/checkpoints"       # 检查点保存路径
export resume_from=""                         # 恢复训练路径(可选)

# 启动训练
torchrun \
  --nnodes=$num_nodes \
  --node_rank=$node_rank \
  --nproc_per_node=8 \
  --master_addr=$master_addr \
  --master_port=$master_port \
  train/pretrain_unified_navit.py \
  --dataset_config_file ./data/configs/example.yaml \
  --llm_path $llm_path \
  --vae_path $vae_path \
  --vit_path $vit_path \
  --use_flex True \
  --resume_from $resume_from \
  --results_dir $output_path \
  --checkpoint_dir $ckpt_path
```

## 2. 数据集配置

### 编辑数据配置文件
修改 `data/configs/example.yaml`：

```yaml
# VLM 监督微调配置
vlm_sft:
  dataset_names:
  - llava_ov                    # 数据集名称
  image_transform_args:
    image_stride: 14            # 图像步长
    max_image_size: 980         # 最大图像尺寸
    min_image_size: 378         # 最小图像尺寸
    max_pixels: 2_007_040       # 最大像素数
  frame_sampler_args:
    max_num_frames: 12          # 最大帧数
    min_num_frames: 8           # 最小帧数
  is_mandatory: true            # 是否必需
  shuffle_lines: True           # 是否打乱数据
  shuffle_seed: 0               # 打乱种子
  num_used_data:
  - 1234567                     # 使用的数据量
  weight: 4                     # 数据集权重

# 文本到图像生成配置
t2i_pretrain:
  dataset_names:
  - t2i                         # T2I数据集
  image_transform_args:
    image_stride: 16
    max_image_size: 1024
    min_image_size: 512
  is_mandatory: false
  num_used_data:
  - 1234567
  weight: 12

# 统一编辑配置  
unified_edit:
  dataset_names:
  - seedxedit_multi             # 编辑数据集
  image_transform_args:
    image_stride: 16
    max_image_size: 1024
    min_image_size: 512
  vit_image_transform_args:
    image_stride: 14
    max_image_size: 518
    min_image_size: 224
  is_mandatory: false
  num_used_data:
  - 1234567
  weight: 2
```

## 3. 分布式训练配置

### 单机多卡训练
```bash
# 8卡训练
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
torchrun --nproc_per_node=8 train/pretrain_unified_navit.py [参数]
```

### 多机训练
```bash
# 节点0 (主节点)
export num_nodes=2
export node_rank=0
export master_addr="192.168.1.100"
export master_port=29500

# 节点1
export num_nodes=2  
export node_rank=1
export master_addr="192.168.1.100"
export master_port=29500
```

## 4. 训练监控

### WandB 配置
```python
# 在训练脚本中添加
import wandb

wandb.init(
    project="bagel-training",
    name="bagel-7b-experiment",
    config={
        "learning_rate": 1e-4,
        "batch_size": 32,
        "epochs": 10,
    }
)
```

### 检查点保存
```python
# 训练参数
save_steps = 1000              # 每1000步保存一次
save_total_limit = 5           # 最多保存5个检查点
logging_steps = 100            # 每100步记录一次日志
```

## 5. 内存和性能优化

### 梯度累积
```python
gradient_accumulation_steps = 4    # 梯度累积步数
per_device_train_batch_size = 2    # 每设备批次大小
```

### 混合精度训练
```python
fp16 = True                        # 启用FP16
bf16 = False                       # 或使用BF16 (A100推荐)
```

### DeepSpeed 配置
```json
{
  "train_batch_size": 32,
  "gradient_accumulation_steps": 4,
  "optimizer": {
    "type": "AdamW",
    "params": {
      "lr": 1e-4,
      "weight_decay": 0.01
    }
  },
  "fp16": {
    "enabled": true
  },
  "zero_optimization": {
    "stage": 2
  }
}
```

## 6. 常见问题解决

### 内存不足
1. 减少 `per_device_train_batch_size`
2. 增加 `gradient_accumulation_steps`
3. 启用 DeepSpeed ZeRO

### 训练不稳定
1. 降低学习率
2. 增加 warmup steps
3. 使用梯度裁剪

### 收敛慢
1. 检查数据质量
2. 调整学习率调度
3. 增加训练数据量
