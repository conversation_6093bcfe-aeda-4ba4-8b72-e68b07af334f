#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 简化演示脚本 (Python 3.13 兼容)
"""

import sys
import os

def test_basic_imports():
    """测试基础导入"""
    print("🧪 测试基础包导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
        
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        
        import PIL
        print(f"✅ Pillow: {PIL.__version__}")
        
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
        
        import yaml
        print("✅ PyYAML: 已导入")
        
        import einops
        print(f"✅ Einops: {einops.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_bagel_modules():
    """检查 BAGEL 模块"""
    print("🔍 检查 BAGEL 模块...")
    
    required_dirs = ['data', 'modeling', 'train', 'eval']
    missing_dirs = []
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ 目录存在: {dir_name}/")
        else:
            print(f"❌ 缺少目录: {dir_name}/")
            missing_dirs.append(dir_name)
    
    return len(missing_dirs) == 0

def main():
    """主函数"""
    print("🥯 BAGEL Python 3.13 兼容性测试")
    print("="*50)
    
    # 测试基础导入
    imports_ok = test_basic_imports()
    
    # 检查模块
    modules_ok = check_bagel_modules()
    
    print("\n" + "="*50)
    if imports_ok and modules_ok:
        print("🎉 基础环境检查通过！")
        print("\n📋 下一步:")
        print("1. 下载模型权重")
        print("2. 运行 inference.ipynb")
        return True
    else:
        print("❌ 环境检查失败，请检查依赖安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
