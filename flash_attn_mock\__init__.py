"""
Flash Attention 模拟模块 (CPU 兼容)
用于在没有 CUDA 的环境中运行 BAGEL
"""

import torch
import torch.nn.functional as F

def flash_attn_varlen_func(
    q, k, v, cu_seqlens_q, cu_seqlens_k, max_seqlen_q, max_seqlen_k,
    dropout_p=0.0, softmax_scale=None, causal=False, window_size=(-1, -1),
    alibi_slopes=None, deterministic=False, return_attn_probs=False
):
    """
    Flash Attention 的 CPU 兼容实现
    使用标准的 PyTorch 注意力机制
    """
    print("⚠️  使用 CPU 兼容的注意力实现 (性能较低)")
    
    # 简化的注意力计算
    batch_size = len(cu_seqlens_q) - 1
    
    # 重新组织输入张量
    outputs = []
    
    for i in range(batch_size):
        start_q = cu_seqlens_q[i]
        end_q = cu_seqlens_q[i + 1]
        start_k = cu_seqlens_k[i]
        end_k = cu_seqlens_k[i + 1]
        
        q_i = q[start_q:end_q]
        k_i = k[start_k:end_k]
        v_i = v[start_k:end_k]
        
        # 标准注意力计算
        if softmax_scale is None:
            softmax_scale = 1.0 / (q_i.size(-1) ** 0.5)
        
        scores = torch.matmul(q_i, k_i.transpose(-2, -1)) * softmax_scale
        
        if causal:
            seq_len = scores.size(-1)
            causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
            scores.masked_fill_(causal_mask, float('-inf'))
        
        attn_weights = F.softmax(scores, dim=-1)
        
        if dropout_p > 0.0:
            attn_weights = F.dropout(attn_weights, p=dropout_p, training=True)
        
        output = torch.matmul(attn_weights, v_i)
        outputs.append(output)
    
    # 合并输出
    result = torch.cat(outputs, dim=0)
    
    if return_attn_probs:
        return result, None  # 不返回注意力权重
    else:
        return result

def flash_attn_func(q, k, v, dropout_p=0.0, softmax_scale=None, causal=False, 
                   window_size=(-1, -1), alibi_slopes=None, deterministic=False, 
                   return_attn_probs=False):
    """
    Flash Attention 函数的 CPU 兼容实现
    """
    print("⚠️  使用 CPU 兼容的注意力实现")
    
    if softmax_scale is None:
        softmax_scale = 1.0 / (q.size(-1) ** 0.5)
    
    scores = torch.matmul(q, k.transpose(-2, -1)) * softmax_scale
    
    if causal:
        seq_len = scores.size(-2)
        causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
        scores.masked_fill_(causal_mask, float('-inf'))
    
    attn_weights = F.softmax(scores, dim=-1)
    
    if dropout_p > 0.0:
        attn_weights = F.dropout(attn_weights, p=dropout_p, training=True)
    
    output = torch.matmul(attn_weights, v)
    
    if return_attn_probs:
        return output, attn_weights
    else:
        return output

# 兼容性别名
flash_attn_with_kvcache = flash_attn_func
flash_attn_kvpacked_func = flash_attn_func
