# BAGEL 修复后的依赖文件
# 核心深度学习框架
torch==2.5.1
torchvision==0.20.1
torchaudio

# Transformers 生态
transformers==4.49.0
accelerate>=0.34.0
huggingface_hub==0.29.1
safetensors==0.4.5
sentencepiece==0.1.99

# 数值计算和科学计算
numpy==1.24.4
scipy==1.10.1
einops==0.8.1

# 图像和视频处理
opencv-python==********
decord==0.6.0
Pillow

# 数据处理
pyarrow==11.0.0
PyYAML==6.0.2

# 可视化和工具
matplotlib==3.7.0
wandb

# Flash Attention (可选，需要 CUDA)
# flash-attn==2.5.8

# 其他工具
requests
