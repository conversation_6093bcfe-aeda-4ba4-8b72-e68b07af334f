Collecting accelerate
  Downloading accelerate-1.7.0-py3-none-any.whl.metadata (19 kB)
Requirement already satisfied: numpy<3.0.0,>=1.17 in c:\python313\lib\site-packages (from accelerate) (2.2.5)
Requirement already satisfied: packaging>=20.0 in c:\python313\lib\site-packages (from accelerate) (25.0)
Collecting psutil (from accelerate)
  Downloading psutil-7.0.0-cp37-abi3-win_amd64.whl.metadata (23 kB)
Requirement already satisfied: pyyaml in c:\python313\lib\site-packages (from accelerate) (6.0.2)
Requirement already satisfied: torch>=2.0.0 in c:\python313\lib\site-packages (from accelerate) (2.7.0)
Requirement already satisfied: huggingface-hub>=0.21.0 in c:\python313\lib\site-packages (from accelerate) (0.32.0)
Requirement already satisfied: safetensors>=0.4.3 in c:\python313\lib\site-packages (from accelerate) (0.5.3)
Requirement already satisfied: filelock in c:\python313\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (3.18.0)
Requirement already satisfied: fsspec>=2023.5.0 in c:\python313\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (2025.5.0)
Requirement already satisfied: requests in c:\python313\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (2.32.3)
Requirement already satisfied: tqdm>=4.42.1 in c:\python313\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (4.67.1)
Requirement already satisfied: typing-extensions>=3.7.4.3 in c:\python313\lib\site-packages (from huggingface-hub>=0.21.0->accelerate) (4.13.2)
Requirement already satisfied: sympy>=1.13.3 in c:\python313\lib\site-packages (from torch>=2.0.0->accelerate) (1.14.0)
Requirement already satisfied: networkx in c:\python313\lib\site-packages (from torch>=2.0.0->accelerate) (3.4.2)
Requirement already satisfied: jinja2 in c:\python313\lib\site-packages (from torch>=2.0.0->accelerate) (3.1.6)
Requirement already satisfied: setuptools in c:\python313\lib\site-packages (from torch>=2.0.0->accelerate) (80.8.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\python313\lib\site-packages (from sympy>=1.13.3->torch>=2.0.0->accelerate) (1.3.0)
Requirement already satisfied: colorama in c:\python313\lib\site-packages (from tqdm>=4.42.1->huggingface-hub>=0.21.0->accelerate) (0.4.6)
Requirement already satisfied: MarkupSafe>=2.0 in c:\python313\lib\site-packages (from jinja2->torch>=2.0.0->accelerate) (3.0.2)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\python313\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\python313\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\python313\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\python313\lib\site-packages (from requests->huggingface-hub>=0.21.0->accelerate) (2025.4.26)
Downloading accelerate-1.7.0-py3-none-any.whl (362 kB)
Downloading psutil-7.0.0-cp37-abi3-win_amd64.whl (244 kB)
Installing collected packages: psutil, accelerate
Successfully installed accelerate-1.7.0 psutil-7.0.0
