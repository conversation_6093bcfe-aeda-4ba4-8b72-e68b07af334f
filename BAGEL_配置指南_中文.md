# 🥯 BAGEL 项目完整配置指南

## 项目概述

BAGEL 是字节跳动开发的统一多模态理解和生成模型，具有以下核心特性：

- **7B 活跃参数** (总共14B参数) 的开源多模态基础模型
- **MoT 架构** - Mixture-of-Transformer-Experts 混合专家架构
- **三大核心功能**：
  - 🔍 **多模态理解** - 图像问答、视觉推理
  - 🎨 **文本到图像生成** - 根据文本描述生成高质量图像
  - ✏️ **图像编辑** - 智能图像修改和操作
- **思维链推理** - 支持 CoT (Chain of Thought) 模式提升性能

## 🚀 快速开始

### 方法一：自动化配置 (推荐)

```bash
# 运行快速启动脚本
python quick_start.py

# 或指定自定义路径
python quick_start.py --model-dir /path/to/models --conda-env my_bagel_env
```

### 方法二：手动配置

#### 1. 环境设置
```bash
# 创建 conda 环境
conda create -n bagel python=3.10 -y
conda activate bagel

# 安装依赖
pip install -r requirements.txt
```

#### 2. 模型下载
```python
from huggingface_hub import snapshot_download

save_dir = "/path/to/save/BAGEL-7B-MoT"
repo_id = "ByteDance-Seed/BAGEL-7B-MoT"

snapshot_download(
    local_dir=save_dir,
    repo_id=repo_id,
    local_dir_use_symlinks=False,
    resume_download=True,
    allow_patterns=["*.json", "*.safetensors", "*.bin", "*.py", "*.md", "*.txt"],
)
```

#### 3. 配置检查
```bash
# 运行配置检查器
python config_checker.py /path/to/BAGEL-7B-MoT/weights
```

## 📋 系统要求

### 硬件要求
- **GPU**: NVIDIA GPU with 24GB+ VRAM (推荐 40GB+)
- **内存**: 32GB+ RAM
- **存储**: 50GB+ 可用空间

### 软件要求
- **操作系统**: Linux/Windows
- **Python**: 3.10
- **CUDA**: 兼容 PyTorch 2.5.1

### 核心依赖
- PyTorch 2.5.1
- Transformers 4.49.0
- Flash Attention 2.5.8
- Accelerate 0.34.0+

## 🎯 使用示例

### 文本到图像生成
```python
from inferencer import InterleaveInferencer

# 初始化推理器
inferencer = InterleaveInferencer(...)

# 生成图像
prompt = "一个穿着仙女裙的女性角色扮演者，在神秘的森林中"
output = inferencer(text=prompt)
display(output['image'])
```

### 图像编辑
```python
from PIL import Image

# 加载图像
image = Image.open('test_images/women.jpg')
prompt = '她穿着同样的衣服，在现代地铁里安静地读报纸'

# 编辑图像
output = inferencer(image=image, text=prompt)
display(output['image'])
```

### 图像理解
```python
image = Image.open('test_images/meme.jpg')
prompt = "请解释这个表情包的幽默之处"

output = inferencer(
    image=image, 
    text=prompt, 
    understanding_output=True
)
print(output['text'])
```

### 思维链推理
```python
prompt = "一辆由小汽车组成的大汽车"

output = inferencer(
    text=prompt, 
    think=True,
    max_think_token_n=1000
)
print("思考过程:", output['text'])
display(output['image'])
```

## ⚙️ 配置参数详解

### 推理参数
```python
inference_hyper = dict(
    cfg_text_scale=4.0,        # 文本引导强度 (1.0-8.0)
    cfg_img_scale=1.0,         # 图像引导强度 (1.0-3.0)
    cfg_interval=[0.4, 1.0],   # CFG 应用区间
    timestep_shift=3.0,        # 时间步偏移 (1.0-5.0)
    num_timesteps=50,          # 去噪步数 (20-100)
    cfg_renorm_min=1.0,        # CFG 重归一化最小值
    cfg_renorm_type="global",  # CFG 重归一化类型
)
```

### GPU 内存配置
```python
# 根据 GPU 内存调整
max_mem_per_gpu = "40GiB"  # A100 80GB
max_mem_per_gpu = "24GiB"  # RTX 4090
max_mem_per_gpu = "32GiB"  # V100
```

## 🏋️ 训练配置

### 训练脚本配置
```bash
# 编辑 scripts/train.sh
export llm_path="/path/to/qwen2-7b"
export vae_path="/path/to/vae/model"
export vit_path="/path/to/siglip/model"
export output_path="/path/to/output"

# 启动训练
bash scripts/train.sh
```

### 数据集配置
编辑 `data/configs/example.yaml` 配置训练数据集。

## 📊 评估配置

### VLM 评估
```bash
# 设置模型路径和输出路径
export model_path="/path/to/BAGEL-7B-MoT"
export output_path="/path/to/eval/results"

# 运行评估
bash scripts/eval/run_eval_vlm.sh
```

### 生成评估
```bash
# GenEval 评估
bash scripts/eval/run_geneval.sh

# WISE 评估  
bash scripts/eval/run_wise.sh
```

## 🔧 常见问题解决

### 内存不足
1. 减少 `max_mem_per_gpu` 设置
2. 降低 `num_timesteps` 参数
3. 使用更小的图像尺寸

### 模型下载失败
1. 使用镜像源: `export HF_ENDPOINT=https://hf-mirror.com`
2. 启用断点续传: `resume_download=True`
3. 分段下载不同文件类型

### CUDA 错误
1. 检查 CUDA 版本兼容性
2. 更新 GPU 驱动
3. 重新安装 PyTorch

### 依赖包冲突
1. 使用虚拟环境隔离
2. 按照 requirements.txt 精确版本安装
3. 检查 Python 版本 (推荐 3.10)

## 📈 性能基准

### 视觉理解
| 模型 | MME | MMBench | MMMU | MM-Vet | MathVista |
|------|-----|---------|------|--------|-----------|
| BAGEL | 2388 | 85.0 | 55.3 | 67.2 | 73.1 |

### 文本到图像生成
| 模型 | GenEval | WISE |
|------|---------|------|
| BAGEL + CoT | 0.88 | 0.70 |

### 图像编辑
| 模型 | GEdit-Bench (SC) | GEdit-Bench (PQ) | IntelligentBench |
|------|------------------|------------------|------------------|
| BAGEL | 7.36 | 6.83 | 44.0 |

## 📚 相关资源

- **官方网站**: https://bagel-ai.org/
- **论文**: https://arxiv.org/abs/2505.14683
- **模型下载**: https://huggingface.co/ByteDance-Seed/BAGEL-7B-MoT
- **在线演示**: https://demo.bagel-ai.org/
- **Discord 社区**: https://discord.gg/Z836xxzy

## 📄 许可证

BAGEL 采用 Apache 2.0 许可证开源。

---

**配置完成后，运行 `python config_checker.py` 验证所有配置是否正确！**
