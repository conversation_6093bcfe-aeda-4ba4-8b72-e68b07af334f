# BAGEL 评估配置指南

## 1. VLM 评估配置

### 数据准备
按照 InternVL2 标准准备评估数据：

```bash
# 创建评估数据目录
mkdir -p eval/vlm/data
cd eval/vlm/data

# 下载各个基准数据集
# MME, MMBench, MMMU, MM-Vet, MathVista, MMVP
```

### 目录结构
```
eval/vlm/data/
├── MathVista/
├── mmbench/
├── mme/
├── MMMU/
├── mm-vet/
└── MMVP/
```

### 运行 VLM 评估
编辑 `scripts/eval/run_eval_vlm.sh`：

```bash
#!/bin/bash
# 设置模型和输出路径
export model_path="/path/to/BAGEL-7B-MoT"
export output_path="/path/to/eval/results"
export GPUS=8                              # GPU数量

# 设置 OpenAI API (用于 MathVista)
export openai_api_key="your_openai_api_key"

# 运行评估
bash scripts/eval/eval_vlm.sh
```

### 各基准评估说明

#### MMBench
- 使用官方评估服务器: https://mmbench.opencompass.org.cn/mmbench-submission
- 上传生成的结果文件进行评估

#### MM-Vet  
- 使用官方评估服务器: https://huggingface.co/spaces/whyu/MM-Vet_Evaluator
- 上传结果文件获取分数

#### MathVista
- 需要设置 OpenAI API key
- 修改 `eval/vlm/eval/mathvista/utilities.py` 中的 `your_api_url`
- 默认使用 GPT-4o-2024-11-20

#### MMMU
- 支持 CoT (思维链) 模式，可提升约2%准确率
- 开放式答案使用 GPT-4o 进行判断

## 2. GenEval 评估配置

### 环境安装
```bash
# 安装依赖
pip install open-clip-torch
pip install clip-benchmark  
pip install --upgrade setuptools

# 安装 MMDetection
sudo pip install -U openmim
sudo mim install mmengine mmcv-full==1.7.2

git clone https://github.com/open-mmlab/mmdetection.git
cd mmdetection
git checkout 2.x
pip install -v -e .
```

### 下载检测器模型
```bash
cd ./eval/gen/geneval
mkdir model
bash ./evaluation/download_models.sh ./model
```

### 运行 GenEval 评估
编辑 `scripts/eval/run_geneval.sh`：

```bash
#!/bin/bash
export model_path="/path/to/BAGEL-7B-MoT"
export output_path="/path/to/geneval/results"

# 设置元数据文件
export metadata_file="./eval/gen/geneval/prompts/evaluation_metadata.jsonl"

# 运行评估
python eval/gen/gen_images_mp.py \
    --model_path $model_path \
    --output_path $output_path \
    --metadata_file $metadata_file
```

## 3. WISE 评估配置

### 运行 WISE 评估
编辑 `scripts/eval/run_wise.sh`：

```bash
#!/bin/bash
export model_path="/path/to/BAGEL-7B-MoT"
export output_path="/path/to/wise/results"
export openai_api_key="your_openai_api_key"

# 启用思维链模式
export use_think=true

# 运行评估
python eval/gen/wise/evaluate.py \
    --model_path $model_path \
    --output_path $output_path \
    --think $use_think
```

### API 配置
修改 `eval/gen/wise/gpt_eval_mp.py` 中的 API 设置：

```python
# 设置 API URL 和版本
api_url = "your_api_url"
gpt_version = "gpt-4o-2024-11-20"
```

## 4. 图像编辑评估

### GEdit-Bench
按照官方指南进行评估：
- 仓库: https://github.com/stepfun-ai/Step1X-Edit/blob/main/GEdit-Bench/EVAL.md
- 包含三个子任务: SC (语义一致性), PQ (感知质量), O (整体质量)

### IntelligentBench
- 智能编辑基准测试
- 文档待更新 (TBD)

## 5. 评估结果分析

### 性能基准

#### 视觉理解 (VLM)
| 模型 | MME ↑ | MMBench ↑ | MMMU ↑ | MM-Vet ↑ | MathVista ↑ |
|------|-------|-----------|--------|----------|-------------|
| BAGEL | 2388 | 85.0 | 55.3 | 67.2 | 73.1 |

#### 文本到图像生成
| 模型 | GenEval ↑ | WISE ↑ |
|------|-----------|--------|
| BAGEL | - | 0.52 |
| BAGEL + CoT | 0.88 | 0.70 |

#### 图像编辑
| 模型 | GEdit-Bench-EN (SC) ↑ | GEdit-Bench-EN (PQ) ↑ | IntelligentBench ↑ |
|------|----------------------|----------------------|-------------------|
| BAGEL | 7.36 | 6.83 | 44.0 |
| BAGEL+CoT | - | - | 55.3 |

## 6. 评估脚本自定义

### 批量评估脚本
```bash
#!/bin/bash
# 运行所有评估
echo "开始 VLM 评估..."
bash scripts/eval/run_eval_vlm.sh

echo "开始 GenEval 评估..."  
bash scripts/eval/run_geneval.sh

echo "开始 WISE 评估..."
bash scripts/eval/run_wise.sh

echo "所有评估完成！"
```

### 结果汇总脚本
```python
# summarize_results.py
import json
import os

def summarize_eval_results(result_dir):
    """汇总评估结果"""
    results = {}
    
    # 读取各个评估结果
    vlm_results = load_vlm_results(result_dir)
    geneval_results = load_geneval_results(result_dir)
    wise_results = load_wise_results(result_dir)
    
    # 汇总结果
    results.update(vlm_results)
    results.update(geneval_results) 
    results.update(wise_results)
    
    return results
```
