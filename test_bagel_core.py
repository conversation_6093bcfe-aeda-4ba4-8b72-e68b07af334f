#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 核心功能测试脚本
测试模型加载和基础推理功能
"""

import os
import sys
import traceback
from pathlib import Path

def test_bagel_imports():
    """测试 BAGEL 相关模块导入"""
    print("🔍 测试 BAGEL 模块导入...")
    
    try:
        # 测试数据处理模块
        from data.transforms import ImageTransform
        from data.data_utils import pil_img2rgb, add_special_tokens
        print("✅ 数据处理模块导入成功")
        
        # 测试模型模块
        from modeling.bagel import (
            BagelConfig, Bagel, Qwen2Config, Qwen2ForCausalLM, 
            SiglipVisionConfig, SiglipVisionModel
        )
        print("✅ BAGEL 模型模块导入成功")
        
        # 测试分词器
        from modeling.qwen2 import Qwen2Tokenizer
        print("✅ Qwen2 分词器导入成功")
        
        # 测试自动编码器
        from modeling.autoencoder import load_ae
        print("✅ 自动编码器模块导入成功")
        
        # 测试推理器
        from inferencer import InterleaveInferencer
        print("✅ 推理器模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_model_config_creation():
    """测试模型配置创建"""
    print("🔧 测试模型配置创建...")
    
    try:
        from modeling.bagel import BagelConfig, Qwen2Config, SiglipVisionConfig
        
        # 创建 LLM 配置
        llm_config = Qwen2Config(
            vocab_size=32000,
            hidden_size=4096,
            intermediate_size=11008,
            num_hidden_layers=32,
            num_attention_heads=32,
            max_position_embeddings=32768,
        )
        llm_config.qk_norm = True
        llm_config.tie_word_embeddings = False
        llm_config.layer_module = "Qwen2MoTDecoderLayer"
        print("✅ LLM 配置创建成功")
        
        # 创建 ViT 配置
        vit_config = SiglipVisionConfig(
            hidden_size=1152,
            intermediate_size=4304,
            num_hidden_layers=27,
            num_attention_heads=16,
            image_size=384,
            patch_size=14,
        )
        vit_config.rope = False
        vit_config.num_hidden_layers = vit_config.num_hidden_layers - 1
        print("✅ ViT 配置创建成功")
        
        # 创建 BAGEL 配置
        config = BagelConfig(
            visual_gen=True,
            visual_und=True,
            llm_config=llm_config,
            vit_config=vit_config,
            vit_max_num_patch_per_side=70,
            connector_act='gelu_pytorch_tanh',
            latent_patch_size=2,
            max_latent_size=64,
        )
        print("✅ BAGEL 配置创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        traceback.print_exc()
        return False

def test_image_transforms():
    """测试图像变换"""
    print("🖼️  测试图像变换...")
    
    try:
        from data.transforms import ImageTransform
        from PIL import Image
        import numpy as np
        
        # 创建图像变换器
        vae_transform = ImageTransform(1024, 512, 16)
        vit_transform = ImageTransform(980, 224, 14)
        print("✅ 图像变换器创建成功")
        
        # 创建测试图像
        test_image = Image.new('RGB', (512, 512), color='red')
        print("✅ 测试图像创建成功")
        
        # 测试变换
        vae_transformed = vae_transform.resize_transform(test_image)
        vit_transformed = vit_transform.resize_transform(test_image)
        print(f"✅ VAE 变换结果尺寸: {vae_transformed.size}")
        print(f"✅ ViT 变换结果尺寸: {vit_transformed.size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像变换测试失败: {e}")
        traceback.print_exc()
        return False

def test_tokenizer_creation():
    """测试分词器创建"""
    print("🔤 测试分词器创建...")
    
    try:
        from modeling.qwen2 import Qwen2Tokenizer
        from data.data_utils import add_special_tokens
        
        # 注意：这里需要实际的分词器文件，我们只测试类的导入
        print("✅ 分词器类导入成功")
        print("⚠️  实际分词器需要模型文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 分词器测试失败: {e}")
        traceback.print_exc()
        return False

def check_model_files(model_path):
    """检查模型文件"""
    print(f"📁 检查模型文件: {model_path}")
    
    if not model_path or not os.path.exists(model_path):
        print("⚠️  模型路径不存在，跳过模型文件检查")
        return False
    
    required_files = [
        'llm_config.json',
        'vit_config.json', 
        'ae.safetensors',
        'ema.safetensors'
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            print(f"✅ 找到文件: {file}")
        else:
            print(f"❌ 缺少文件: {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️  缺少 {len(missing_files)} 个模型文件")
        return False
    else:
        print("✅ 所有模型文件都存在")
        return True

def create_download_script():
    """创建模型下载脚本"""
    print("📝 创建模型下载脚本...")
    
    download_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGEL 模型下载脚本
"""

import os
from huggingface_hub import snapshot_download

def download_bagel_model(save_dir="./models/BAGEL-7B-MoT"):
    """下载 BAGEL 模型"""
    print(f"开始下载 BAGEL 模型到: {save_dir}")
    
    repo_id = "ByteDance-Seed/BAGEL-7B-MoT"
    cache_dir = os.path.join(save_dir, "cache")
    
    try:
        snapshot_download(
            cache_dir=cache_dir,
            local_dir=save_dir,
            repo_id=repo_id,
            local_dir_use_symlinks=False,
            resume_download=True,
            allow_patterns=[
                "*.json", 
                "*.safetensors", 
                "*.bin", 
                "*.py", 
                "*.md", 
                "*.txt"
            ],
        )
        print("✅ 模型下载完成！")
        print(f"模型路径: {save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        model_dir = sys.argv[1]
    else:
        model_dir = "./models/BAGEL-7B-MoT"
    
    success = download_bagel_model(model_dir)
    sys.exit(0 if success else 1)
'''
    
    with open("download_bagel_model.py", "w", encoding="utf-8") as f:
        f.write(download_script)
    
    print("✅ 已创建 download_bagel_model.py")
    return True

def main():
    """主函数"""
    print("🥯 BAGEL 核心功能测试")
    print("="*60)
    
    # 测试步骤
    tests = [
        ("BAGEL 模块导入", test_bagel_imports),
        ("模型配置创建", test_model_config_creation),
        ("图像变换", test_image_transforms),
        ("分词器创建", test_tokenizer_creation),
        ("创建下载脚本", create_download_script),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n📋 测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    # 检查模型文件（如果提供了路径）
    model_path = None
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
        print(f"\\n📋 测试: 模型文件检查")
        if check_model_files(model_path):
            print("✅ 模型文件检查 - 通过")
            passed_tests += 1
        else:
            print("❌ 模型文件检查 - 失败")
        total_tests += 1
    
    # 总结
    print("\\n" + "="*60)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！BAGEL 核心功能正常")
        print("\\n📋 下一步:")
        if not model_path:
            print("1. 下载模型: python download_bagel_model.py")
            print("2. 运行完整测试: python test_bagel_core.py ./models/BAGEL-7B-MoT/weights")
        print("3. 启动 Jupyter: jupyter notebook")
        print("4. 打开 inference.ipynb 开始使用")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
